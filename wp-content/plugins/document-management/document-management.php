<?php
/**
 * Plugin Name: 文档管理系统
 * Plugin URI: https://example.com/document-management
 * Description: 一个强大的文档管理系统插件，提供栏目管理功能，支持多级菜单的拖拽排序。
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * Text Domain: document-management
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 定义插件常量
define('DOCUMENT_MANAGEMENT_VERSION', '1.0.0');
define('DOCUMENT_MANAGEMENT_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('DOCUMENT_MANAGEMENT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('DOCUMENT_MANAGEMENT_PLUGIN_FILE', __FILE__);

/**
 * 文档管理系统主类
 */
class Document_Management {
    
    /**
     * 单例实例
     */
    private static $instance = null;
    
    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 构造函数
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * 初始化插件
     */
    private function init() {
        // 注册激活和停用钩子
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // 加载插件文本域
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // 初始化插件功能
        add_action('init', array($this, 'init_plugin'));

        // 添加重写规则
        add_action('init', array($this, 'add_rewrite_rules'));

        // 处理前端查询
        add_action('parse_request', array($this, 'parse_frontend_request'));
        
        // 管理员初始化
        if (is_admin()) {
            add_action('admin_init', array($this, 'admin_init'));
            add_action('load-post-new.php', array($this, 'handle_parent_id'));
            add_action('admin_footer', array($this, 'add_parent_script'));
        }
    }
    
    /**
     * 插件激活时执行
     */
    public function activate() {
        // 检查WordPress版本
        if (version_compare(get_bloginfo('version'), '5.0', '<')) {
            wp_die(__('此插件需要WordPress 5.0或更高版本。', 'document-management'));
        }
        
        // 检查PHP版本
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            wp_die(__('此插件需要PHP 7.4或更高版本。', 'document-management'));
        }
        
        // 添加重写规则
        $this->add_rewrite_rules();

        // 刷新重写规则
        flush_rewrite_rules();
    }
    
    /**
     * 插件停用时执行
     */
    public function deactivate() {
        // 清理临时数据
        flush_rewrite_rules();
    }
    
    /**
     * 加载插件文本域
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'document-management',
            false,
            dirname(plugin_basename(__FILE__)) . '/languages'
        );
    }
    
    /**
     * 初始化插件功能
     */
    public function init_plugin() {
        // 加载核心文件
        $this->load_dependencies();
    }
    
    /**
     * 管理员初始化
     */
    public function admin_init() {
        // 加载管理员相关功能
        $this->load_admin_dependencies();
    }
    
    /**
     * 加载依赖文件
     */
    private function load_dependencies() {
        // 加载核心类文件
        if (file_exists(DOCUMENT_MANAGEMENT_PLUGIN_DIR . 'includes/class-column-manager.php')) {
            require_once DOCUMENT_MANAGEMENT_PLUGIN_DIR . 'includes/class-column-manager.php';

            // 初始化栏目管理器
            if (class_exists('Column_Manager')) {
                Column_Manager::get_instance();
            }
        } else {
            add_action('admin_notices', array($this, 'missing_files_notice'));
        }
    }

    /**
     * 显示缺失文件通知
     */
    public function missing_files_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php _e('文档管理系统插件：核心文件缺失，请重新安装插件。', 'document-management'); ?></p>
        </div>
        <?php
    }
    
    /**
     * 加载管理员依赖文件
     */
    private function load_admin_dependencies() {
        // 加载AJAX处理文件
        if (file_exists(DOCUMENT_MANAGEMENT_PLUGIN_DIR . 'includes/ajax-handlers.php')) {
            require_once DOCUMENT_MANAGEMENT_PLUGIN_DIR . 'includes/ajax-handlers.php';
        }
    }
    
    /**
     * 获取插件版本
     */
    public function get_version() {
        return DOCUMENT_MANAGEMENT_VERSION;
    }
    
    /**
     * 获取插件目录路径
     */
    public function get_plugin_dir() {
        return DOCUMENT_MANAGEMENT_PLUGIN_DIR;
    }
    
    /**
     * 获取插件URL
     */
    public function get_plugin_url() {
        return DOCUMENT_MANAGEMENT_PLUGIN_URL;
    }

    /**
     * 添加重写规则
     */
    public function add_rewrite_rules() {
        // 获取设置中的URL slug
        $settings = get_option('document_management_settings', array('url_slug' => 'docs'));
        $url_slug = $settings['url_slug'];

        // 添加动态路由
        add_rewrite_rule('^' . $url_slug . '/?$', 'index.php?docs_page=home', 'top');
        add_rewrite_rule('^' . $url_slug . '/([^/]+)/?$', 'index.php?docs_page=single&doc_slug=$matches[1]', 'top');

        // 添加查询变量
        add_filter('query_vars', array($this, 'add_query_vars'));
    }

    /**
     * 添加查询变量
     */
    public function add_query_vars($vars) {
        $vars[] = 'docs_page';
        $vars[] = 'doc_slug';
        return $vars;
    }

    /**
     * 处理前端请求
     */
    public function parse_frontend_request($wp) {
        if (isset($wp->query_vars['docs_page'])) {
            $this->load_frontend_template($wp->query_vars);
            exit;
        }
    }

    /**
     * 加载前端模板
     */
    private function load_frontend_template($query_vars) {
        // 加载前端样式和脚本
        wp_enqueue_style('docs-frontend-css', DOCUMENT_MANAGEMENT_PLUGIN_URL . 'assets/css/frontend.css', array(), DOCUMENT_MANAGEMENT_VERSION);
        wp_enqueue_script('docs-frontend-js', DOCUMENT_MANAGEMENT_PLUGIN_URL . 'assets/js/frontend.js', array('jquery'), DOCUMENT_MANAGEMENT_VERSION, true);

        // 获取设置
        $settings = get_option('document_management_settings', array('url_slug' => 'docs'));

        // 传递数据给JavaScript
        wp_localize_script('docs-frontend-js', 'docsData', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('docs_frontend_nonce'),
            'currentPage' => $query_vars['docs_page'],
            'docSlug' => isset($query_vars['doc_slug']) ? $query_vars['doc_slug'] : '',
            'siteTitle' => get_bloginfo('name'),
            'urlSlug' => $settings['url_slug']
        ));

        // 包含模板文件
        include DOCUMENT_MANAGEMENT_PLUGIN_DIR . 'templates/frontend.php';
    }

    /**
     * 获取文档URL
     */
    public function get_docs_url() {
        $settings = get_option('document_management_settings', array('url_slug' => 'docs'));
        return home_url('/' . $settings['url_slug'] . '/');
    }

    /**
     * 处理parent_id参数
     */
    public function handle_parent_id() {
        if (isset($_GET['parent_id']) && is_numeric($_GET['parent_id'])) {
            $parent_id = intval($_GET['parent_id']);
            // 验证父文章存在
            if (get_post($parent_id)) {
                // 将parent_id存储在session中，以便在保存时使用
                if (!session_id()) {
                    session_start();
                }
                $_SESSION['column_parent_id'] = $parent_id;
            }
        }
    }

    /**
     * 添加设置父级的JavaScript
     */
    public function add_parent_script() {
        global $pagenow;

        if ($pagenow === 'post-new.php' && isset($_SESSION['column_parent_id'])) {
            $parent_id = $_SESSION['column_parent_id'];
            $parent_post = get_post($parent_id);
            ?>
            <script type="text/javascript">
            jQuery(document).ready(function($) {
                // 添加父级信息提示
                if ($('#post-body').length) {
                    $('#post-body').prepend(
                        '<div class="notice notice-info"><p><strong>正在创建子栏目</strong><br>' +
                        '父栏目: <?php echo esc_js($parent_post ? $parent_post->post_title : ''); ?></p></div>'
                    );
                }

                // 在保存时设置父级
                $('#publish, #save-post').on('click', function() {
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'post_parent',
                        value: '<?php echo $parent_id; ?>'
                    }).appendTo('#post');
                });
            });
            </script>
            <?php
            // 清除session中的parent_id
            unset($_SESSION['column_parent_id']);
        }
    }
}

/**
 * 获取插件主实例
 */
function document_management() {
    return Document_Management::get_instance();
}

// 启动插件
document_management();
