/**
 * 文档前端交互脚本
 */
(function($) {
    'use strict';
    
    var DocsFrontend = {
        
        /**
         * 初始化
         */
        init: function() {
            this.loadMenu();
            this.bindEvents();
            this.handleInitialLoad();
        },
        
        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;
            
            // 菜单点击事件
            $(document).on('click', '.docs-menu-link', function(e) {
                e.preventDefault();
                var postId = $(this).data('post-id');
                var slug = $(this).data('slug');
                
                if (postId) {
                    self.loadDocument(postId, slug);
                    self.setActiveMenuItem($(this));
                    self.updateUrl(slug);
                }
            });
            
            // 浏览器前进后退
            window.addEventListener('popstate', function(e) {
                if (e.state && e.state.docSlug) {
                    self.loadDocumentBySlug(e.state.docSlug);
                } else {
                    self.showWelcome();
                }
            });
        },
        
        /**
         * 处理初始加载
         */
        handleInitialLoad: function() {
            if (docsData.docSlug) {
                this.loadDocumentBySlug(docsData.docSlug);
            }
        },
        
        /**
         * 加载菜单
         */
        loadMenu: function() {
            var self = this;
            
            $.ajax({
                url: docsData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'get_docs_menu',
                    nonce: docsData.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $('#docs-menu').html(response.data.html);
                    } else {
                        $('#docs-menu').html('<div class="error">菜单加载失败</div>');
                    }
                },
                error: function() {
                    $('#docs-menu').html('<div class="error">菜单加载失败</div>');
                }
            });
        },
        
        /**
         * 加载文档内容
         */
        loadDocument: function(postId, slug) {
            var self = this;
            
            this.showLoading();
            
            $.ajax({
                url: docsData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'get_doc_content',
                    nonce: docsData.nonce,
                    post_id: postId
                },
                success: function(response) {
                    if (response.success) {
                        $('#docs-article').html(response.data.content);
                        document.title = response.data.title + ' - ' + docsData.siteTitle;
                    } else {
                        $('#docs-article').html('<div class="error">文档加载失败</div>');
                    }
                },
                error: function() {
                    $('#docs-article').html('<div class="error">文档加载失败</div>');
                },
                complete: function() {
                    self.hideLoading();
                }
            });
        },
        
        /**
         * 通过slug加载文档
         */
        loadDocumentBySlug: function(slug) {
            var self = this;
            var menuLink = $('.docs-menu-link[data-slug="' + slug + '"]');
            
            if (menuLink.length) {
                var postId = menuLink.data('post-id');
                this.loadDocument(postId, slug);
                this.setActiveMenuItem(menuLink);
            } else {
                // 如果菜单还没加载完成，等待一下再试
                setTimeout(function() {
                    self.loadDocumentBySlug(slug);
                }, 500);
            }
        },
        
        /**
         * 设置活动菜单项
         */
        setActiveMenuItem: function(element) {
            $('.docs-menu-link').removeClass('active');
            element.addClass('active');
        },
        
        /**
         * 更新URL
         */
        updateUrl: function(slug) {
            var newUrl = '/' + docsData.urlSlug + '/' + slug;
            history.pushState({docSlug: slug}, '', newUrl);
        },
        
        /**
         * 显示欢迎内容
         */
        showWelcome: function() {
            $('#docs-article').html(
                '<div class="welcome-content">' +
                    '<h2>欢迎来到文档中心</h2>' +
                    '<p>请从左侧菜单选择要查看的文档。</p>' +
                '</div>'
            );
            $('.docs-menu-link').removeClass('active');
            document.title = '文档中心 - ' + docsData.siteTitle;
        },
        
        /**
         * 显示加载状态
         */
        showLoading: function() {
            $('#loading-overlay').show();
        },
        
        /**
         * 隐藏加载状态
         */
        hideLoading: function() {
            $('#loading-overlay').hide();
        }
    };
    
    // 文档就绪时初始化
    $(document).ready(function() {
        DocsFrontend.init();
    });
    
})(jQuery);
