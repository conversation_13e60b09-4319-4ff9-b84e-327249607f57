/**
 * 文档前端样式
 */

/* 重置和基础样式 */
.docs-frontend {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #2d3748;
    background: #f7fafc;
}

.docs-frontend * {
    box-sizing: border-box;
}

/* 容器 */
.docs-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部导航 */
.docs-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.docs-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    text-align: center;
}

.docs-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.docs-title a {
    color: #fff;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.docs-title a:hover {
    opacity: 0.8;
}



/* 主要内容区域 */
.docs-main {
    flex: 1;
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    gap: 2rem;
    padding: 2rem;
}

/* 左侧边栏 */
.docs-sidebar {
    width: 300px;
    flex-shrink: 0;
}

.docs-sidebar-content {
    background: #fff;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 100px;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
}

.docs-sidebar-content h3 {
    margin: 0 0 1rem 0;
    color: #2d3748;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.5rem;
}

/* 文档菜单 */
.docs-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.docs-menu-item {
    margin-bottom: 0.5rem;
}

.docs-menu-link {
    display: block;
    padding: 0.75rem 1rem;
    color: #4a5568;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    border-left: 3px solid transparent;
}

.docs-menu-link:hover {
    background: linear-gradient(135deg, #f0f4ff 0%, #e8f0ff 100%);
    color: #667eea;
    border-left-color: #667eea;
    transform: translateX(2px);
}

.docs-menu-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border-left-color: #fff;
}

/* 子菜单 */
.docs-submenu {
    list-style: none;
    margin: 0.5rem 0 0 0;
    padding: 0;
    border-left: 3px solid #667eea;
    margin-left: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 0 6px 6px 0;
}

.docs-submenu .docs-menu-link {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
    color: #4a5568;
    font-weight: 500;
    position: relative;
}

.docs-submenu .docs-menu-link:before {
    content: "└";
    color: #a0aec0;
    margin-right: 0.5rem;
    font-weight: normal;
}

.docs-submenu .docs-menu-link:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.docs-submenu .docs-menu-link.active {
    color: #fff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 右侧内容区域 */
.docs-content {
    flex: 1;
    min-width: 0;
}

.docs-content-inner {
    background: #fff;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    min-height: 600px;
}

/* 文章内容 */
.docs-article {
    max-width: none;
}

.docs-article h1 {
    color: #2d3748;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
    line-height: 1.2;
}

.docs-article h2 {
    color: #2d3748;
    font-size: 2rem;
    font-weight: 600;
    margin: 2rem 0 1rem 0;
    line-height: 1.3;
}

.docs-article h3 {
    color: #4a5568;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 1.5rem 0 0.75rem 0;
}

.docs-article p {
    margin: 0 0 1rem 0;
    line-height: 1.7;
    color: #4a5568;
}

.docs-article ul, .docs-article ol {
    margin: 0 0 1rem 1.5rem;
    line-height: 1.7;
}

.docs-article li {
    margin-bottom: 0.5rem;
    color: #4a5568;
}

.docs-article a {
    color: #667eea;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.3s ease;
}

.docs-article a:hover {
    border-bottom-color: #667eea;
}

/* 欢迎内容 */
.welcome-content {
    text-align: center;
    padding: 4rem 2rem;
    color: #718096;
}

.welcome-content h2 {
    color: #2d3748;
    margin-bottom: 1rem;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 2rem;
    color: #718096;
    font-style: italic;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    background: #fff;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .docs-main {
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
    }
    
    .docs-sidebar {
        width: 100%;
        order: 2;
    }
    
    .docs-sidebar-content {
        position: static;
        max-height: none;
    }
    
    .docs-content-inner {
        padding: 1.5rem;
    }
    
    .docs-header-content {
        padding: 1rem;
    }
    
    .docs-article h1 {
        font-size: 2rem;
    }
}
